<div class="room-management">
  <header class="header">
    <div class="title">
      <h2>ROOM MANAGEMENT</h2>
    </div>
    <div class="right-section">
      <div class="select-building">
        <mat-form-field>
          <mat-label>Building</mat-label>
          <mat-select [(value)]="buildingCode" canSelectNullableOptions>
            @for (option of options; track option) {
            <mat-option [value]="option.code">{{ option.name }}</mat-option>
            }
          </mat-select>
        </mat-form-field>
      </div>
      <div class="search-box">
        <mat-form-field class="example-form-field">
          <mat-label>Search</mat-label>
          <input
            placeholder="..."
            matInput
            type="text"
            [(ngModel)]="searchText"
          />
          @if (searchText) {
          <button
            id="icon-search"
            matSuffix
            matIconButton
            aria-label="btnSearch"
            (click)="searchText=''"
          >
            <mat-icon>search</mat-icon>
          </button>
          }
        </mat-form-field>
      </div>
    </div>
  </header>

  <div class="rooms">
    <app-room *ngFor="let room of rooms" [room]="room"></app-room>
  </div>
</div>

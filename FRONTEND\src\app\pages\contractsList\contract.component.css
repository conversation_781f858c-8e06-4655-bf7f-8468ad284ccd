/* Search Container */
.search-container {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.search-field {
  width: 100%;
  max-width: 500px;
}

/* Table Container - Center the table */
.table-container {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
  overflow-x: auto;
}

.contracts-table {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  overflow: hidden;
}

/* Paginator Container */
.paginator-container {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

/* Filters Container */
.filters {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

/* Status Styles */
.status {
  padding: 4px 10px;
  border-radius: 6px;
  font-weight: 500;
  text-transform: capitalize;
  font-size: 0.875rem;
  color: white;
}

.status.active {
  background-color: #4fd1c5;
}
.status.expire {
  background-color: #f56565;
}
.status.terminate {
  background-color: #a0aec0;
}
.status.unsigned {
  background-color: #ecc94b;
  color: #333;
}
.status.unpaid {
  background-color: #a0aec0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .table-container {
    margin: 1rem 0;
  }

  .contracts-table {
    font-size: 0.875rem;
  }

  .filters {
    flex-direction: column;
    align-items: center;
  }

  .search-field {
    max-width: 100%;
  }
}

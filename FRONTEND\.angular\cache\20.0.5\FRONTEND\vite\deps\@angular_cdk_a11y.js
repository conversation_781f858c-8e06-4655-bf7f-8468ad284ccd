import {
  A11yModule,
  ActiveDescendant<PERSON>eyManager,
  AriaDescriber,
  CDK_DESCRIBEDBY_HOST_ATTRIBUTE,
  CDK_DESCRIBEDBY_ID_PREFIX,
  CdkAriaLive,
  CdkMonitorFocus,
  CdkTrapFocus,
  ConfigurableFocusTrap,
  ConfigurableFocusTrapFactory,
  EventListenerFocusTrapInertStrategy,
  FOCUS_MONITOR_DEFAULT_OPTIONS,
  FOCUS_TRAP_INERT_STRATEGY,
  FocusKeyManager,
  FocusMonitor,
  FocusMonitorDetectionMode,
  FocusTrap,
  FocusTrapFactory,
  HighContrastMode,
  HighContrastModeDetector,
  INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,
  INPUT_MODALITY_DETECTOR_OPTIONS,
  InputModalityDetector,
  InteractivityChecker,
  IsFocusableConfig,
  LIVE_ANNOUNCER_DEFAULT_OPTIONS,
  LIVE_ANNOUNCER_ELEMENT_TOKEN,
  LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY,
  List<PERSON>eyManager,
  LiveAnnouncer,
  MESSAGES_CONTAINER_ID,
  NOOP_TREE_KEY_MANAGER_FACTORY,
  NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER,
  NoopTreeKeyManager,
  TREE_KEY_MANAGER,
  TREE_KEY_MANAGER_FACTORY,
  TREE_KEY_MANAGER_FACTORY_PROVIDER,
  TreeKeyManager,
  _IdGenerator,
  addAriaReferencedId,
  getAriaReferenceIds,
  isFakeMousedownFromScreenReader,
  isFakeTouchstartFromScreenReader,
  removeAriaReferencedId
} from "./chunk-P3OTXEQP.js";
import "./chunk-26NMGGMF.js";
import "./chunk-ONJMER4J.js";
import "./chunk-BTPRSWYL.js";
import "./chunk-PZ7PGUCM.js";
export {
  A11yModule,
  ActiveDescendantKeyManager,
  AriaDescriber,
  CDK_DESCRIBEDBY_HOST_ATTRIBUTE,
  CDK_DESCRIBEDBY_ID_PREFIX,
  CdkAriaLive,
  CdkMonitorFocus,
  CdkTrapFocus,
  ConfigurableFocusTrap,
  ConfigurableFocusTrapFactory,
  EventListenerFocusTrapInertStrategy,
  FOCUS_MONITOR_DEFAULT_OPTIONS,
  FOCUS_TRAP_INERT_STRATEGY,
  FocusKeyManager,
  FocusMonitor,
  FocusMonitorDetectionMode,
  FocusTrap,
  FocusTrapFactory,
  HighContrastMode,
  HighContrastModeDetector,
  INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,
  INPUT_MODALITY_DETECTOR_OPTIONS,
  InputModalityDetector,
  InteractivityChecker,
  IsFocusableConfig,
  LIVE_ANNOUNCER_DEFAULT_OPTIONS,
  LIVE_ANNOUNCER_ELEMENT_TOKEN,
  LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY,
  ListKeyManager,
  LiveAnnouncer,
  MESSAGES_CONTAINER_ID,
  NOOP_TREE_KEY_MANAGER_FACTORY,
  NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER,
  NoopTreeKeyManager,
  TREE_KEY_MANAGER,
  TREE_KEY_MANAGER_FACTORY,
  TREE_KEY_MANAGER_FACTORY_PROVIDER,
  TreeKeyManager,
  _IdGenerator,
  addAriaReferencedId,
  getAriaReferenceIds,
  isFakeMousedownFromScreenReader,
  isFakeTouchstartFromScreenReader,
  removeAriaReferencedId
};

<div class="filters mb-4 flex gap-4 items-center">
  <mat-form-field appearance="fill">
    <mat-label>Building</mat-label>
    <mat-select [(ngModel)]="filters.building" (selectionChange)="applyFilters()">
      <mat-option value="">All</mat-option>
      <mat-option *ngFor="let b of buildings" [value]="b">{{ b }}</mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field appearance="fill">
    <mat-label>Room</mat-label>
    <mat-select [(ngModel)]="filters.room" (selectionChange)="applyFilters()">
      <mat-option value="">All</mat-option>
      <mat-option *ngFor="let r of rooms" [value]="r">{{ r }}</mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field appearance="fill">
    <mat-label>Status</mat-label>
    <mat-select [(ngModel)]="filters.status" (selectionChange)="applyFilters()">
      <mat-option value="">All</mat-option>
      <mat-option *ngFor="let s of statuses" [value]="s">{{ s }}</mat-option>
    </mat-select>
  </mat-form-field>
</div>

<table mat-table [dataSource]="filteredcontracts.slice(pageIndex * pageSize, (pageIndex + 1) * pageSize)" class="mat-elevation-z1 w-full">

  <ng-container matColumnDef="building">
    <th mat-header-cell *matHeaderCellDef> Building </th>
    <td mat-cell *matCellDef="let inv"> {{inv.building}} </td>
  </ng-container>

  <ng-container matColumnDef="room">
    <th mat-header-cell *matHeaderCellDef> Room </th>
    <td mat-cell *matCellDef="let inv"> {{inv.room}} </td>
  </ng-container>

  <ng-container matColumnDef="start">
    <th mat-header-cell *matHeaderCellDef> Start Day </th>
    <td mat-cell *matCellDef="let inv"> {{inv.start}} </td>
  </ng-container>

  <ng-container matColumnDef="end">
    <th mat-header-cell *matHeaderCellDef> End Date </th>
    <td mat-cell *matCellDef="let inv"> {{inv.end}} </td>
  </ng-container>

  <ng-container matColumnDef="deposit">
    <th mat-header-cell *matHeaderCellDef> Deposit </th>
    <td mat-cell *matCellDef="let inv"> {{inv.deposit}} </td>
  </ng-container>

  <ng-container matColumnDef="total">
    <th mat-header-cell *matHeaderCellDef> Total (VND) </th>
    <td mat-cell *matCellDef="let inv"> {{inv.total | number}} </td>
  </ng-container>

  <ng-container matColumnDef="status">
    <th mat-header-cell *matHeaderCellDef> Status </th>
    <td mat-cell *matCellDef="let inv">
      <span [ngClass]="'status ' + inv.status.toLowerCase()">{{ inv.status }}</span>
    </td>
  </ng-container>

  <tr mat-header-row *matHeaderRowDef="['building', 'room', 'start', 'end', 'deposit', 'total', 'status']"></tr>
  <tr mat-row *matRowDef="let row; columns: ['building', 'room', 'start', 'end', 'deposit', 'total', 'status'];"></tr>
</table>

<mat-paginator [length]="filteredcontracts.length"
               [pageSize]="pageSize"
               [pageSizeOptions]="[5, 10, 20]"
               (page)="onPageChange($event)">
</mat-paginator>

import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  AngularAppEngine,
  InlineCriticalCssProcessor,
  PrerenderFallback,
  RenderMode,
  createRequestHandler,
  destroyAngularServerApp,
  extractRoutesAndCreateRouteTree,
  getOrCreateAngularServerApp,
  getRoutesFromAngularRouterConfig,
  provideServerRendering,
  setAngularAppEngineManifest,
  setAngularAppManifest,
  withAppShell,
  withRoutes
} from "./chunk-RCRSYHHI.js";
import "./chunk-VKBSNTSN.js";
import "./chunk-HN6ZRJL2.js";
import "./chunk-E6DX2GLV.js";
import "./chunk-GXWBT5FU.js";
import "./chunk-6DU2HRTW.js";
export {
  AngularAppEngine,
  PrerenderFallback,
  RenderMode,
  createRequestHandler,
  provideServerRendering,
  withAppShell,
  withRoutes,
  InlineCriticalCssProcessor as ɵInlineCriticalCssProcessor,
  destroyAngularServerApp as ɵdestroyAngularServerApp,
  extractRoutesAndCreateRouteTree as ɵextractRoutesAndCreateRouteTree,
  getOrCreateAngularServerApp as ɵgetOrCreateAngularServerApp,
  getRoutesFromAngularRouterConfig as ɵgetRoutesFromAngularRouterConfig,
  setAngularAppEngineManifest as ɵsetAngularAppEngineManifest,
  setAngularAppManifest as ɵsetAngularAppManifest
};

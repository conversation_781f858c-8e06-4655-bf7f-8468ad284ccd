import {AfterViewInit, Component, ViewChild} from '@angular/core';
import {MatPaginator, MatPaginatorModule} from '@angular/material/paginator';
import {MatSort, MatSortModule} from '@angular/material/sort';
import {MatTableDataSource, MatTableModule} from '@angular/material/table';
import {MatInputModule} from '@angular/material/input';
import {MatFormFieldModule} from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

interface Contract {
  building: string;
  room: number;
  start: string;
  end: string;
  deposit: number;
  total: number;
  status: 'Active' | 'Expire' | 'Terminate' | 'Unsigned' ;
}

@Component({
  selector: 'app-contract',
  templateUrl: './contractList.html',
  styleUrls: ['./contractList.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatTableModule,
    MatPaginatorModule,
    MatInputModule,
    MatIconModule,
    MatSortModule
  ],
})
export class ContractComponent implements AfterViewInit {
  displayedColumns: string[] = ['building', 'room', 'start', 'end', 'deposit', 'total', 'status'];
  dataSource: MatTableDataSource<Contract>;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor() {
    // Create contract data
    const contracts: Contract[] = [
    { building: 'ABCHome', room: 201, start: '6/2025', end: '10/7/2025', deposit: 100000, total: 2000000, status: 'Active' },
    { building: 'QHome', room: 310, start: '5/2025', end: '10/6/2025', deposit: 100000, total: 1100000, status: 'Expire' },
    { building: 'QHome', room: 101, start: '7/2025', end: '10/8/2025', deposit: 100000, total: 3000000, status: 'Terminate' },
    { building: 'QHome', room: 102, start: '7/2025', end: '10/8/2025', deposit: 200000, total: 3000000, status: 'Unsigned' },
    { building: 'QHome', room: 102, start: '7/2025', end: '10/8/2025', deposit: 300000, total: 3000000, status:  'Terminate'},
    { building: 'ABCHome', room: 201, start: '6/2025', end: '10/7/2025', deposit: 400000, total: 2000000, status: 'Active' },
    { building: 'QHome', room: 310, start: '5/2025', end: '10/6/2025', deposit: 1500000, total: 1100000, status: 'Expire' },
    { building: 'QHome', room: 101, start: '7/2025', end: '10/8/2025', deposit: 2500000, total: 3000000, status: 'Terminate' },
    { building: 'QHome', room: 102, start: '7/2025', end: '10/8/2025', deposit: 3100000, total: 3000000, status: 'Unsigned' },
    { building: 'QHome', room: 102, start: '7/2025', end: '10/8/2025', deposit: 3200000, total: 3000000, status: 'Terminate'},
    { building: 'ABCHome', room: 201, start: '6/2025', end: '10/7/2025', deposit: 100000, total: 2000000, status: 'Active' },
    { building: 'QHome', room: 310, start: '5/2025', end: '10/6/2025', deposit: 100000, total: 1100000, status: 'Expire' },
    { building: 'QHome', room: 101, start: '7/2025', end: '10/8/2025', deposit: 100000, total: 3000000, status: 'Terminate' },
    { building: 'QHome', room: 102, start: '7/2025', end: '10/8/2025', deposit: 200000, total: 3000000, status: 'Unsigned' },
    { building: 'QHome', room: 102, start: '7/2025', end: '10/8/2025', deposit: 300000, total: 3000000, status:  'Terminate'},
    { building: 'ABCHome', room: 201, start: '6/2025', end: '10/7/2025', deposit: 400000, total: 2000000, status: 'Active' },
    { building: 'QHome', room: 310, start: '5/2025', end: '10/6/2025', deposit: 1500000, total: 1100000, status: 'Expire' },
    { building: 'QHome', room: 101, start: '7/2025', end: '10/8/2025', deposit: 2500000, total: 3000000, status: 'Terminate' },
    { building: 'QHome', room: 102, start: '7/2025', end: '10/8/2025', deposit: 3100000, total: 3000000, status: 'Unsigned' },
    { building: 'QHome', room: 102, start: '7/2025', end: '10/8/2025', deposit: 3200000, total: 3000000, status: 'Terminate'},
    ];

    // Assign the data to the data source for the table to render
    this.dataSource = new MatTableDataSource(contracts);
  }

  // Filter properties
  searchTerm = '';
  buildings = ['ABCHome', 'QHome'];
  rooms = [101, 102, 201, 310];
  statuses = ['Active', 'Expire', 'Terminate', 'Unsigned'];

  filters = {
    building: '',
    room: '',
    status: ''
  };

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  applyFilters() {
    // Apply dropdown filters
    let filteredData = this.dataSource.data.filter((contract: Contract) => {
      return (!this.filters.building || contract.building === this.filters.building) &&
             (!this.filters.room || contract.room.toString() === this.filters.room) &&
             (!this.filters.status || contract.status === this.filters.status);
    });

    this.dataSource.data = filteredData;

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

}

<div class="main">
  <div class="header">
    <h2 class="title">TENANT MANAGEMENT</h2>
    <input type="text" placeholder="Search..." #input />
  </div>

  <div class="mat-elevation-z8">
    <table mat-table [dataSource]="dataSource" matSort>
      <!-- avatar Column -->
      <ng-container matColumnDef="avatar">
        <th mat-header-cell *matHeaderCellDef mat-sort-header></th>
        <td mat-cell *matCellDef="let row">{{row.avatar}}</td>
      </ng-container>

      <!-- name Column -->
      <ng-container matColumnDef="name">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
        <td mat-cell *matCellDef="let row">{{row.progress}}%</td>
      </ng-container>

      <!-- bdate Column -->
      <ng-container matColumnDef="bdate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Birthday</th>
        <td mat-cell *matCellDef="let row">{{row.name}}</td>
      </ng-container>

      <!-- Fruit Column -->
      <ng-container matColumnDef="fruit">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Fruit</th>
        <td mat-cell *matCellDef="let row">{{row.fruit}}</td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

      <!-- Row shown when there is no matching data. -->
      <tr class="mat-row" *matNoDataRow>
        <td class="mat-cell" colspan="4">
          No data matching the filter "{{input.value}}"
        </td>
      </tr>
    </table>

    <mat-paginator
      [pageSizeOptions]="[5, 10, 25, 100]"
      aria-label="Select page of users"
    ></mat-paginator>
  </div>
</div>

<p>invoicetable works!</p>
<div class="filter-toolbar">
  <button mat-button>
    <mat-icon>filter_list</mat-icon> Filter
  </button>

  <mat-form-field>
    <mat-label>Building</mat-label>
    <mat-select [(ngModel)]="filter.building">
      <mat-option *ngFor="let b of buildings" [value]="b">{{ b }}</mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field>
    <mat-label>Room</mat-label>
    <mat-select [(ngModel)]="filter.room">
      <mat-option *ngFor="let r of rooms" [value]="r">{{ r }}</mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field>
    <mat-label>Date</mat-label>
    <input matInput [matDatepicker]="picker" [(ngModel)]="filter.date" />
    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
    <mat-datepicker #picker></mat-datepicker>
  </mat-form-field>

  <mat-form-field>
    <mat-label>Status</mat-label>
    <mat-select [(ngModel)]="filter.status">
      <mat-option value="paid">Paid</mat-option>
      <mat-option value="overdue">Overdue</mat-option>
      <mat-option value="unpaid">Unpaid</mat-option>
    </mat-select>
  </mat-form-field>

  <button mat-button (click)="clearFilters()">❌ Clear All</button>
  <button mat-raised-button color="primary" (click)="export()">⬇ Export</button>
</div>

<table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z1">

  <!-- Building Column -->
  <ng-container matColumnDef="building">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>Building</th>
    <td mat-cell *matCellDef="let row">{{ row.building }}</td>
  </ng-container>

  <!-- Room Column -->
  <ng-container matColumnDef="room">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>Room</th>
    <td mat-cell *matCellDef="let row">{{ row.room }}</td>
  </ng-container>

  <!-- Month Column -->
  <ng-container matColumnDef="month">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>Month</th>
    <td mat-cell *matCellDef="let row">{{ row.month }}</td>
  </ng-container>

  <!-- Due Date Column -->
  <ng-container matColumnDef="dueDate">
    <th mat-header-cell *matHeaderCellDef>Due Date</th>
    <td mat-cell *matCellDef="let row">{{ row.dueDate | date }}</td>
  </ng-container>

  <!-- Total Amount -->
  <ng-container matColumnDef="amount">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>TotalAmount (VND)</th>
    <td mat-cell *matCellDef="let row">{{ row.amount | number }}</td>
  </ng-container>

  <!-- Status -->
  <ng-container matColumnDef="status">
    <th mat-header-cell *matHeaderCellDef>Status</th>
    <td mat-cell *matCellDef="let row">
      <mat-chip [color]="getStatusColor(row.status)" selected>{{ row.status }}</mat-chip>
    </td>
  </ng-container>

  <!-- Row definitions -->
  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
  <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

</table>

<mat-paginator [length]="total" [pageSize]="8"></mat-paginator>

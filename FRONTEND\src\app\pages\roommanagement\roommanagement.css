* {
  margin: 0;
  padding: 0;
}

.room-management {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: var(--padding-margin);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 6%;
  padding: var(--padding-margin);
  background-color: #fff;
  border-radius: var(--border-radius);
}

.title {
  padding: 10px;
}

.right-section {
  margin-right: 10px;
}

.rooms {
  overflow-y: auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(20%, 1fr));
  gap: var(--padding-margin);
  margin-top: var(--padding-margin);
  padding-bottom: 16px;
}

.rooms::-webkit-scrollbar {
  display: none;
}

.status-available,
.status-occupied,
.status-maintenance {
  display: flex;
  align-items: center;
  gap: 0 5px;
}

.status {
  height: 10px;
  width: 10px;
  border-radius: 50%;
  background-color: black;
}

.status-Available {
  background-color: #16c09861;
}

.status-Occupied {
  background-color: #b5b7c0;
}

.status-Maintenance {
  background-color: #ffc5c5;
}

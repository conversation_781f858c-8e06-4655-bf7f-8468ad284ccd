* {
  margin: 0;
  padding: 0;
}

.room-management {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: var(--padding-margin);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 6%;
  padding: var(--padding-margin);
  background-color: #fff;
  border-radius: var(--border-radius);
}

.right-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

::ng-deep .mdc-line-ripple::before,
::ng-deep .mdc-line-ripple::after {
  display: none !important;
}

::ng-deep .mat-mdc-text-field-wrapper {
  border-radius: 20px !important;
}

::ng-deep .mdc-text-field {
  padding: 0px 30px !important;
}

/* .mdc-text-field */

::ng-deep .mat-mdc-form-field-subscript-wrapper {
  display: none;
}

::ng-deep .mat-mdc-form-field-icon-prefix {
  padding-left: 16px !important;
}

#icon-search {
  border: none;
  background: none;
  display: flex;
  align-content: center;
}

#search-label {
  margin-left: 30px;
}

.title {
  padding: 10px;
}

.right-section {
  margin-right: 10px;
}

.rooms {
  overflow-y: auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(20%, 1fr));
  gap: var(--padding-margin);
  margin-top: var(--padding-margin);
  padding-bottom: 16px;
}

.rooms::-webkit-scrollbar {
  display: none;
}

.status-available,
.status-occupied,
.status-maintenance {
  display: flex;
  align-items: center;
  gap: 0 5px;
}

.status {
  height: 10px;
  width: 10px;
  border-radius: 50%;
  background-color: black;
}

.status-Available {
  background-color: #16c09861;
}

.status-Occupied {
  background-color: #b5b7c0;
}

.status-Maintenance {
  background-color: #ffc5c5;
}

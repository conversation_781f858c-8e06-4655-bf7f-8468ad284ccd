<div class="main">
  <!-- Header with Title and Search -->
  <div class="header-container">
    <h2 class="page-title">BUILDING MANAGEMENT</h2>
    <div class="search-container">
      <mat-form-field appearance="outline" class="search-field">
        <input
          matInput
          [(ngModel)]="searchTerm"
          (input)="applyFilters()"
          placeholder="Search"
        />
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>
    </div>
  </div>

  <!-- Table Container -->
  <div class="table-container">
    <!-- Filters Row -->
    <div class="filters-container">
      <div></div>
      <div></div>
      <div></div>
      <mat-form-field appearance="outline" class="filter-field">
        <mat-select
          [(ngModel)]="filters.building"
          (selectionChange)="applyFilters()"
          placeholder="Building"
        >
          <mat-option value="">Building</mat-option>
          <mat-option *ngFor="let b of buildings" [value]="b"
            >{{ b }}</mat-option
          >
        </mat-select>
        <mat-icon matSuffix>apartment</mat-icon>
      </mat-form-field>
    </div>

    <div class="mat-elevation-z8">
      <table
        mat-table
        matSort
        (matSortChange)="announceSortChange($event)"
        [dataSource]="dataSource"
        class="contracts-table"
      >
        <ng-container matColumnDef="code">
          <th
            mat-header-cell
            *matHeaderCellDef
            mat-sort-header
            sortActionDescription="Sort by code"
          >
            Room
          </th>
          <td mat-cell *matCellDef="let inv">{{inv.code}}</td>
        </ng-container>

        <ng-container matColumnDef="building">
          <th
            mat-header-cell
            *matHeaderCellDef
            mat-sort-header
            sortActionDescription="Sort by building"
          >
            Building
          </th>
          <td mat-cell *matCellDef="let inv">{{inv.building}}</td>
        </ng-container>

        <ng-container matColumnDef="address">
          <th
            mat-header-cell
            *matHeaderCellDef
            mat-sort-header
            sortActionDescription="Sort by address"
          >
            Address
          </th>
          <td mat-cell *matCellDef="let inv">{{inv.address}}</td>
        </ng-container>

        <ng-container matColumnDef="tenants">
          <th
            mat-header-cell
            *matHeaderCellDef
            mat-sort-header
            sortActionDescription="Sort by tenants"
          >
            Tenants
          </th>
          <td mat-cell *matCellDef="let inv">{{inv.tenants}}</td>
        </ng-container>

        <ng-container matColumnDef="rooms">
          <th
            mat-header-cell
            *matHeaderCellDef
            mat-sort-header
            sortActionDescription="Sort by rooms"
          >
            Rooms
          </th>
          <td mat-cell *matCellDef="let inv">{{inv.rooms}}</td>
        </ng-container>

        <ng-container matColumnDef="available">
          <th
            mat-header-cell
            *matHeaderCellDef
            mat-sort-header
            sortActionDescription="Sort by available"
          >
            Available
          </th>
          <td mat-cell *matCellDef="let inv">{{inv.available}}</td>
        </ng-container>

        <ng-container matColumnDef="occupied">
          <th
            mat-header-cell
            *matHeaderCellDef
            mat-sort-header
            sortActionDescription="Sort by occupied"
          >
            Occupied
          </th>
          <td mat-cell *matCellDef="let inv">{{inv.occupied | number}}</td>
        </ng-container>

        <ng-container matColumnDef="maintenance">
          <th
            mat-header-cell
            *matHeaderCellDef
            mat-sort-header
            sortActionDescription="Sort by maintenance"
          >
            Maintenance
          </th>
          <td mat-cell *matCellDef="let inv">{{ inv.maintenance }}</td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>

      <mat-paginator
        [pageSizeOptions]="[5, 10, 20]"
        showFirstLastButtons
        aria-label="Select page of periodic elements"
      >
      </mat-paginator>
    </div>
  </div>
</div>

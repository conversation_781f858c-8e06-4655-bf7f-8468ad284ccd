{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/css-pixel-value-C_HEqLhI.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/coercion.mjs"], "sourcesContent": ["/** Coerces a value to a CSS pixel value. */\nfunction coerceCssPixelValue(value) {\n    if (value == null) {\n        return '';\n    }\n    return typeof value === 'string' ? value : `${value}px`;\n}\n\nexport { coerceCssPixelValue as c };\n\n", "export { _ as _isNumberValue, a as coerceElement, c as coerceNumberProperty } from './element-x4z00URv.mjs';\nexport { c as coerceArray } from './array-I1yfCXUO.mjs';\nexport { c as coerceCssPixelValue } from './css-pixel-value-C_HEqLhI.mjs';\nimport '@angular/core';\n\n/** Coerces a data-bound value (typically a string) to a boolean. */\nfunction coerceBooleanProperty(value) {\n    return value != null && `${value}` !== 'false';\n}\n\n/**\n * Coerces a value to an array of trimmed non-empty strings.\n * Any input that is not an array, `null` or `undefined` will be turned into a string\n * via `toString()` and subsequently split with the given separator.\n * `null` and `undefined` will result in an empty array.\n * This results in the following outcomes:\n * - `null` -&gt; `[]`\n * - `[null]` -&gt; `[\"null\"]`\n * - `[\"a\", \"b \", \" \"]` -&gt; `[\"a\", \"b\"]`\n * - `[1, [2, 3]]` -&gt; `[\"1\", \"2,3\"]`\n * - `[{ a: 0 }]` -&gt; `[\"[object Object]\"]`\n * - `{ a: 0 }` -&gt; `[\"[object\", \"Object]\"]`\n *\n * Useful for defining CSS classes or table columns.\n * @param value the value to coerce into an array of strings\n * @param separator split-separator if value isn't an array\n */\nfunction coerceStringArray(value, separator = /\\s+/) {\n    const result = [];\n    if (value != null) {\n        const sourceValues = Array.isArray(value) ? value : `${value}`.split(separator);\n        for (const sourceValue of sourceValues) {\n            const trimmedString = `${sourceValue}`.trim();\n            if (trimmedString) {\n                result.push(trimmedString);\n            }\n        }\n    }\n    return result;\n}\n\nexport { coerceBooleanProperty, coerceStringArray };\n\n"], "mappings": ";;;AACA,SAAS,oBAAoB,OAAO;AAChC,MAAI,SAAS,MAAM;AACf,WAAO;AAAA,EACX;AACA,SAAO,OAAO,UAAU,WAAW,QAAQ,GAAG,KAAK;AACvD;;;ACAA,SAAS,sBAAsB,OAAO;AAClC,SAAO,SAAS,QAAQ,GAAG,KAAK,OAAO;AAC3C;AAmBA,SAAS,kBAAkB,OAAO,YAAY,OAAO;AACjD,QAAM,SAAS,CAAC;AAChB,MAAI,SAAS,MAAM;AACf,UAAM,eAAe,MAAM,QAAQ,KAAK,IAAI,QAAQ,GAAG,KAAK,GAAG,MAAM,SAAS;AAC9E,eAAW,eAAe,cAAc;AACpC,YAAM,gBAAgB,GAAG,WAAW,GAAG,KAAK;AAC5C,UAAI,eAAe;AACf,eAAO,KAAK,aAAa;AAAA,MAC7B;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;", "names": []}
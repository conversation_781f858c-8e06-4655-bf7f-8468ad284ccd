<!-- Header with Title and Search -->
<div class="header-container">
  <h1 class="page-title">CONTRACTS</h1>
  <div class="search-container">
    <mat-form-field appearance="outline" class="search-field">
      <mat-label>Search</mat-label>
      <input
        matInput
        placeholder="Search..."
        [(ngModel)]="searchTerm"
        (input)="applyFilters()"
      />
      <mat-icon matSuffix>search</mat-icon>
    </mat-form-field>
  </div>
</div>

<!-- Table Container -->
<div class="table-container">
  <!-- Filters Row -->
  <div class="filters-row">
    <div class="filters-container">
      <mat-form-field appearance="outline" class="filter-field">
        <mat-select
          [(ngModel)]="filters.building"
          (selectionChange)="applyFilters()"
          placeholder="Building"
        >
          <mat-option value="">Building</mat-option>
          <mat-option *ngFor="let b of buildings" [value]="b"
            >{{ b }}</mat-option
          >
        </mat-select>
        <mat-icon matSuffix>apartment</mat-icon>
      </mat-form-field>

      <mat-form-field appearance="outline" class="filter-field">
        <mat-select
          [(ngModel)]="filters.room"
          (selectionChange)="applyFilters()"
          placeholder="Room"
        >
          <mat-option value="">Room</mat-option>
          <mat-option *ngFor="let r of rooms" [value]="r">{{ r }}</mat-option>
        </mat-select>
        <mat-icon matSuffix>meeting_room</mat-icon>
      </mat-form-field>

      <mat-form-field appearance="outline" class="filter-field">
        <mat-select
          [(ngModel)]="filters.status"
          (selectionChange)="applyFilters()"
          placeholder="Status"
        >
          <mat-option value="">Status</mat-option>
          <mat-option *ngFor="let s of statuses" [value]="s"
            >{{ s }}</mat-option
          >
        </mat-select>
        <mat-icon matSuffix>info</mat-icon>
      </mat-form-field>
    </div>
  </div>

  <table mat-table [dataSource]="dataSource"  matSort class="contracts-table" >
    <ng-container matColumnDef="building">
      <th mat-header-cell *matHeaderCellDef>Building</th>
      <td mat-cell *matCellDef="let inv">{{inv.building}}</td>
    </ng-container>

    <ng-container matColumnDef="room">
      <th mat-header-cell *matHeaderCellDef>Room</th>
      <td mat-cell *matCellDef="let inv">{{inv.room}}</td>
    </ng-container>

    <ng-container matColumnDef="start">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>Start Day</th>
      <td mat-cell *matCellDef="let inv">{{inv.start}}</td>
    </ng-container>

    <ng-container matColumnDef="end">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>End Date</th>
      <td mat-cell *matCellDef="let inv">{{inv.end}}</td>
    </ng-container>

    <ng-container matColumnDef="deposit">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>Deposit</th>
      <td mat-cell *matCellDef="let inv">{{inv.deposit}}</td>
    </ng-container>

    <ng-container matColumnDef="total">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>Total (VND)</th>
      <td mat-cell *matCellDef="let inv">{{inv.total | number}}</td>
    </ng-container>

    <ng-container matColumnDef="status">
      <th mat-header-cell *matHeaderCellDef>Status</th>
      <td mat-cell *matCellDef="let inv">
        <span [ngClass]="'status ' + inv.status.toLowerCase()"
          >{{ inv.status }}
        </span>
      </td>
    </ng-container>
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
  </table>
  <mat-paginator [pageSizeOptions]="[5, 10, 15, 20]" showFirstLastButtons></mat-paginator>
</div>

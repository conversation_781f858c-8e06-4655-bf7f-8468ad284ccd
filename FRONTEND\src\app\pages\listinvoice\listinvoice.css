/* Page Container with Scroll */
:host {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: auto;
  background-color: var(--bg-color);
}
/* Custom Scrollbar for Table */
:host::-webkit-scrollbar {
  display: none;
}
/* Header Container */
.header-container {
  border-radius: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 1rem;
  height: 8%;
  background-color: #ffffff;
  margin: var(--padding-margin) ;
}

/* .page-title {
  padding: 10px;
} */

.search-container {
  display: flex;
  align-items: center;
}

.search-field {
  width: 300px;
}

.search-field .mat-mdc-form-field-subscript-wrapper {
  display: none;
}

/* Filters Row */
.filters-row {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: #ffffff;
  margin-top: 0.5rem;
}

/* .filter-label {
  font-weight: 500;
  color: #6b7280;
  min-width: 50px;
} */
.filter-label-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: #f8fafc;
  color: #374151;
  font-weight: 500;
  padding: 6px 14px;
  border: 1px solid #e5e7eb;
  border-radius: 20px;
  cursor: default;
  font-size: 0.875rem;
  height: 38px;
}

.filter-label-btn mat-icon {
  font-size: 18px;
  color: #6b7280;
}

.filters-container {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-field {
  min-width: 150px;
}

.filter-field .mat-mdc-form-field-subscript-wrapper {
  display: none;
}

/* Main Content Area */
.main-content {
  flex: 1;
  padding-bottom: 1rem;
}

/* Table Container */
.table-container {
  margin-left: var(--border-radius);
  margin-right: var(--border-radius);
  margin-bottom: 10vh;
  background: white;
  border-spacing: 0;
  border-radius: var(--border-radius);
  overflow: auto;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.table-container::-webkit-scrollbar {
  display: none;
}
.contracts-table {
  width: 100%;
  background: white;
}

/* Table Headers */
.contracts-table .mat-mdc-header-cell {
  /* border-radius: 20px 20px 0 0; */
  background-color: #f8fafc;
  color: #374151;
  font-weight: 600;
  border-bottom: 1px solid #e5e7eb;
  padding: 16px 12px;
}

.contracts-table .mat-mdc-cell {
  padding: 16px 12px;
  border-bottom: 1px solid #f3f4f6;
}

.contracts-table .mat-mdc-row:hover {
  background-color: #f9fafb;
}

/* Custom Paginator */
.custom-paginator {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* Left side - Showing info */
.paginator-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  font-size: 0.875rem;
}

.page-size-select {
  padding: 0.25rem 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
}

.page-size-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

/* Right side - Page numbers */
.pagination-numbers {
  display: flex;
  gap: 2px;
}

.page-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #e0f2fe;
  color: #0369a1;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-btn:hover:not(:disabled) {
  background: #bae6fd;
  color: #0c4a6e;
}

.page-btn.active {
  background: #0369a1;
  color: white;
}

.page-btn:disabled {
  background: #f3f4f6;
  color: #9ca3af;
  cursor: not-allowed;
}

.prev-btn, .next-btn {
  font-size: 1.2rem;
  font-weight: bold;
}

/* Status Styles */
.status {
  padding: 4px 10px;
  border-radius: 6px;
  font-weight: 500;
  text-transform: capitalize;
  font-size: 0.875rem;
  color: white;
}

.status.paid {
  background-color: #4fd1c5;
}
.status.overdue {
  background-color: #f56565;
}

.status.unpaid {
  background-color: #a0aec0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .search-field {
    width: 100%;
  }

  .filters-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .filters-container {
    width: 100%;
    flex-direction: column;
  }

  .filter-field {
    width: 100%;
    min-width: unset;
  }

  .contracts-table {
    font-size: 0.875rem;
  }

  .contracts-table .mat-mdc-header-cell,
  .contracts-table .mat-mdc-cell {
    padding: 12px 8px;
  }

  .custom-paginator {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
    margin: 0 1rem 1.5rem 1rem;
  }

  .paginator-info {
    justify-content: center;
  }

  .pagination-numbers {
    justify-content: center;
  }

  .page-btn {
    width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }
}

﻿// <auto-generated />
using System;
using BACKEND.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace BACKEND.Migrations
{
    [DbContext(typeof(MotelMateDbContext))]
    [Migration("20250707040755_MyDatabase")]
    partial class MyDatabase
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("BACKEND.Models.Account", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<DateTime>("Bdate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CCCD")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("nvarchar(8)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("Role")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("URLAvatar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.ToTable("AspNetUsers", (string)null);

                    b.HasDiscriminator().HasValue("Account");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("BACKEND.Models.Asset", b =>
                {
                    b.Property<int>("AssetID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AssetID"));

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("AssetID");

                    b.ToTable("Asset");
                });

            modelBuilder.Entity("BACKEND.Models.Building", b =>
                {
                    b.Property<int>("BuildingID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("BuildingID"));

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("OwnerID")
                        .HasColumnType("int");

                    b.HasKey("BuildingID");

                    b.HasIndex("OwnerID");

                    b.ToTable("Building");
                });

            modelBuilder.Entity("BACKEND.Models.Contract", b =>
                {
                    b.Property<int>("ContractID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ContractID"));

                    b.Property<decimal>("Deposit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateOnly>("EndDate")
                        .HasColumnType("date");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("RoomID")
                        .HasColumnType("int");

                    b.Property<DateOnly>("StartDate")
                        .HasColumnType("date");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ContractID");

                    b.HasIndex("RoomID");

                    b.ToTable("Contract");
                });

            modelBuilder.Entity("BACKEND.Models.ContractDetail", b =>
                {
                    b.Property<int>("ContractID")
                        .HasColumnType("int");

                    b.Property<int>("TenantID")
                        .HasColumnType("int");

                    b.Property<DateOnly>("StartDate")
                        .HasColumnType("date");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<bool?>("IsRoomRepresentative")
                        .HasColumnType("bit");

                    b.HasKey("ContractID", "TenantID", "StartDate");

                    b.HasIndex("TenantID");

                    b.ToTable("ContractDetail");
                });

            modelBuilder.Entity("BACKEND.Models.Invoice", b =>
                {
                    b.Property<int>("InvoiceID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("InvoiceID"));

                    b.Property<int?>("ContractID")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateAt")
                        .HasColumnType("datetime2");

                    b.Property<DateOnly>("PeriodEnd")
                        .HasColumnType("date");

                    b.Property<DateOnly>("PeriodStart")
                        .HasColumnType("date");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("InvoiceID");

                    b.HasIndex("ContractID");

                    b.ToTable("Invoice");
                });

            modelBuilder.Entity("BACKEND.Models.InvoiceDetail", b =>
                {
                    b.Property<int>("InvoiceID")
                        .HasColumnType("int");

                    b.Property<int>("ServiceID")
                        .HasColumnType("int");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.HasKey("InvoiceID", "ServiceID");

                    b.HasIndex("ServiceID");

                    b.ToTable("InvoiceDetails");
                });

            modelBuilder.Entity("BACKEND.Models.Noti", b =>
                {
                    b.Property<int>("NotiID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("NotiID"));

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreateAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("OwnerID")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("NotiID");

                    b.HasIndex("OwnerID");

                    b.ToTable("Noti");
                });

            modelBuilder.Entity("BACKEND.Models.NotiRecipient", b =>
                {
                    b.Property<int>("NotiID")
                        .HasColumnType("int");

                    b.Property<int>("TenantID")
                        .HasColumnType("int");

                    b.HasKey("NotiID", "TenantID");

                    b.HasIndex("TenantID");

                    b.ToTable("NotiRecipient");
                });

            modelBuilder.Entity("BACKEND.Models.Request", b =>
                {
                    b.Property<int>("RequestID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("RequestID"));

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreateAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("OwnerID")
                        .HasColumnType("int");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("TenantID")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("RequestID");

                    b.HasIndex("OwnerID");

                    b.HasIndex("TenantID");

                    b.ToTable("Request");
                });

            modelBuilder.Entity("BACKEND.Models.Room", b =>
                {
                    b.Property<int>("RoomID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("RoomID"));

                    b.Property<double>("Area")
                        .HasColumnType("float");

                    b.Property<int?>("BuildingID")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("RoomID");

                    b.HasIndex("BuildingID");

                    b.ToTable("Room");
                });

            modelBuilder.Entity("BACKEND.Models.RoomAsset", b =>
                {
                    b.Property<int>("AssetID")
                        .HasColumnType("int");

                    b.Property<int>("RoomID")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.HasKey("AssetID", "RoomID");

                    b.HasIndex("RoomID");

                    b.ToTable("RoomAsset");
                });

            modelBuilder.Entity("BACKEND.Models.RoomImage", b =>
                {
                    b.Property<int>("RoomID")
                        .HasColumnType("int");

                    b.Property<string>("ImageURL")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("RoomID", "ImageURL");

                    b.ToTable("RoomImage");
                });

            modelBuilder.Entity("BACKEND.Models.Service", b =>
                {
                    b.Property<int>("ServiceID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ServiceID"));

                    b.Property<decimal>("CustomerPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("InitialPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool?>("IsTiered")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Unit")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ServiceID");

                    b.ToTable("Services");
                });

            modelBuilder.Entity("BACKEND.Models.ServiceTier", b =>
                {
                    b.Property<int>("ServiceTierID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ServiceTierID"));

                    b.Property<int>("FromQuantity")
                        .HasColumnType("int");

                    b.Property<decimal>("GovUnitPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("ServiceID")
                        .HasColumnType("int");

                    b.Property<int>("ToQuantity")
                        .HasColumnType("int");

                    b.HasKey("ServiceTierID");

                    b.HasIndex("ServiceID");

                    b.ToTable("ServiceTiers");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<int>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<int>", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<int>", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("BACKEND.Models.Owner", b =>
                {
                    b.HasBaseType("BACKEND.Models.Account");

                    b.Property<long>("AccountName")
                        .HasColumnType("bigint");

                    b.Property<long>("AccountNo")
                        .HasColumnType("bigint");

                    b.Property<long>("BankCode")
                        .HasColumnType("bigint");

                    b.HasDiscriminator().HasValue("Owner");
                });

            modelBuilder.Entity("BACKEND.Models.Tenant", b =>
                {
                    b.HasBaseType("BACKEND.Models.Account");

                    b.HasDiscriminator().HasValue("Tenant");
                });

            modelBuilder.Entity("BACKEND.Models.Building", b =>
                {
                    b.HasOne("BACKEND.Models.Owner", "Owner")
                        .WithMany("Buildings")
                        .HasForeignKey("OwnerID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Owner");
                });

            modelBuilder.Entity("BACKEND.Models.Contract", b =>
                {
                    b.HasOne("BACKEND.Models.Room", "Room")
                        .WithMany("Contracts")
                        .HasForeignKey("RoomID");

                    b.Navigation("Room");
                });

            modelBuilder.Entity("BACKEND.Models.ContractDetail", b =>
                {
                    b.HasOne("BACKEND.Models.Contract", "Contract")
                        .WithMany("ContractDetail")
                        .HasForeignKey("ContractID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BACKEND.Models.Tenant", "Tenant")
                        .WithMany("ContractDetails")
                        .HasForeignKey("TenantID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Contract");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("BACKEND.Models.Invoice", b =>
                {
                    b.HasOne("BACKEND.Models.Contract", "Contract")
                        .WithMany("Invoice")
                        .HasForeignKey("ContractID");

                    b.Navigation("Contract");
                });

            modelBuilder.Entity("BACKEND.Models.InvoiceDetail", b =>
                {
                    b.HasOne("BACKEND.Models.Invoice", "Invoice")
                        .WithMany("InvoiceDetail")
                        .HasForeignKey("InvoiceID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BACKEND.Models.Service", "Service")
                        .WithMany("InvoiceDetail")
                        .HasForeignKey("ServiceID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Invoice");

                    b.Navigation("Service");
                });

            modelBuilder.Entity("BACKEND.Models.Noti", b =>
                {
                    b.HasOne("BACKEND.Models.Owner", "Owner")
                        .WithMany("OwnerNoties")
                        .HasForeignKey("OwnerID");

                    b.Navigation("Owner");
                });

            modelBuilder.Entity("BACKEND.Models.NotiRecipient", b =>
                {
                    b.HasOne("BACKEND.Models.Noti", "Noti")
                        .WithMany("NotiRecipients")
                        .HasForeignKey("NotiID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BACKEND.Models.Tenant", "Tenant")
                        .WithMany("TenantNoties")
                        .HasForeignKey("TenantID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Noti");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("BACKEND.Models.Request", b =>
                {
                    b.HasOne("BACKEND.Models.Owner", "Owner")
                        .WithMany("OwnerRequests")
                        .HasForeignKey("OwnerID");

                    b.HasOne("BACKEND.Models.Tenant", "Tenant")
                        .WithMany("TenantRequests")
                        .HasForeignKey("TenantID");

                    b.Navigation("Owner");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("BACKEND.Models.Room", b =>
                {
                    b.HasOne("BACKEND.Models.Building", "Building")
                        .WithMany("Room")
                        .HasForeignKey("BuildingID");

                    b.Navigation("Building");
                });

            modelBuilder.Entity("BACKEND.Models.RoomAsset", b =>
                {
                    b.HasOne("BACKEND.Models.Asset", "Asset")
                        .WithMany("RoomAsset")
                        .HasForeignKey("AssetID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BACKEND.Models.Room", "Room")
                        .WithMany("RoomAssets")
                        .HasForeignKey("RoomID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Asset");

                    b.Navigation("Room");
                });

            modelBuilder.Entity("BACKEND.Models.RoomImage", b =>
                {
                    b.HasOne("BACKEND.Models.Room", "Room")
                        .WithMany("RoomImages")
                        .HasForeignKey("RoomID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Room");
                });

            modelBuilder.Entity("BACKEND.Models.ServiceTier", b =>
                {
                    b.HasOne("BACKEND.Models.Service", "Service")
                        .WithMany("ServiceTier")
                        .HasForeignKey("ServiceID");

                    b.Navigation("Service");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<int>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole<int>", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<int>", b =>
                {
                    b.HasOne("BACKEND.Models.Account", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<int>", b =>
                {
                    b.HasOne("BACKEND.Models.Account", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<int>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole<int>", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BACKEND.Models.Account", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<int>", b =>
                {
                    b.HasOne("BACKEND.Models.Account", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("BACKEND.Models.Asset", b =>
                {
                    b.Navigation("RoomAsset");
                });

            modelBuilder.Entity("BACKEND.Models.Building", b =>
                {
                    b.Navigation("Room");
                });

            modelBuilder.Entity("BACKEND.Models.Contract", b =>
                {
                    b.Navigation("ContractDetail");

                    b.Navigation("Invoice");
                });

            modelBuilder.Entity("BACKEND.Models.Invoice", b =>
                {
                    b.Navigation("InvoiceDetail");
                });

            modelBuilder.Entity("BACKEND.Models.Noti", b =>
                {
                    b.Navigation("NotiRecipients");
                });

            modelBuilder.Entity("BACKEND.Models.Room", b =>
                {
                    b.Navigation("Contracts");

                    b.Navigation("RoomAssets");

                    b.Navigation("RoomImages");
                });

            modelBuilder.Entity("BACKEND.Models.Service", b =>
                {
                    b.Navigation("InvoiceDetail");

                    b.Navigation("ServiceTier");
                });

            modelBuilder.Entity("BACKEND.Models.Owner", b =>
                {
                    b.Navigation("Buildings");

                    b.Navigation("OwnerNoties");

                    b.Navigation("OwnerRequests");
                });

            modelBuilder.Entity("BACKEND.Models.Tenant", b =>
                {
                    b.Navigation("ContractDetails");

                    b.Navigation("TenantNoties");

                    b.Navigation("TenantRequests");
                });
#pragma warning restore 612, 618
        }
    }
}

* {
  margin: 0;
  padding: 0;
}

.card {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #ffffff;
  box-shadow: var(--box-shadow);
  border-radius: var(--border-radius);
}

.image {
  width: 90%;
  height: 60%;
  margin: var(--padding-margin) auto;
  border-radius: var(--border-radius);
}

.image img {
  width: 100%;
  height: 100%;
  border-radius: var(--border-radius);
}

.info {
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  width: 90%;
  height: 40%;
  padding: var(--padding-margin);
}

.section1 {
  display: flex;
  justify-content: space-between;
}

.groupPrice {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-price {
  display: flex;
}

.groupMaxguest {
  display: flex;
  align-items: center;
  gap: 0 5px;
}

.avatars {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.avatars .imgs {
  display: flex;
  align-items: center;
}

.avatars img {
  height: 40px;
  aspect-ratio: 1 / 1;
  margin-right: -10px;
  object-fit: cover;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.room-status {
  display: flex;
  align-items: center;
  gap: 0 5px;
}

.icon-status {
  height: 10px;
  aspect-ratio: 1 / 1;
  border-radius: 50%;
}

.avatars img:hover {
  height: 60px;
  aspect-ratio: 1 / 1;
  margin-right: 10px;
  margin-left: 10px;
  object-fit: cover;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.status-Available {
  background-color: #16c09861;
}

.status-Occupied {
  background-color: #b5b7c0;
}

.status-Maintenance {
  background-color: #ffc5c5;
}
